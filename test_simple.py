#!/usr/bin/env python3
"""
Simple test script for the API
"""

import requests
import json
import time

def test_api():
    url = "http://localhost:8188/forward"
    
    # Test với bot_token và chat_id giả
    payload = {
        "chat_id": "123456789",
        "text_message": "🧪 Test message from API",
        "bot_token": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",  # Fake token
        "parse_mode": "HTML"
    }
    
    print("🧪 Testing API endpoint...")
    print(f"📤 URL: {url}")
    print(f"📦 Payload: {json.dumps(payload, indent=2)}")
    
    try:
        print("\n⏳ Sending request...")
        start_time = time.time()
        
        response = requests.post(url, json=payload, timeout=10)
        
        end_time = time.time()
        print(f"⏱️  Response time: {end_time - start_time:.2f}s")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ API is working!")
        else:
            print("⚠️  API returned error (expected with fake token)")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is server running?")
    except Exception as e:
        print(f"💥 Error: {e}")

if __name__ == "__main__":
    test_api()
