#!/usr/bin/env python3
"""
Test script for HTTP Proxy functionality
"""

import requests
import json
import time
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_proxy_connection():
    """Test proxy connection"""
    print("🔍 Testing HTTP Proxy Connection...")
    
    HTTP_PROXY_ENABLED = os.getenv('HTTP_PROXY_ENABLED', 'False').lower() == 'true'
    HTTP_PROXY_HOST = os.getenv('HTTP_PROXY_HOST', '')
    HTTP_PROXY_PORT = int(os.getenv('HTTP_PROXY_PORT', '8080'))
    HTTP_PROXY_USERNAME = os.getenv('HTTP_PROXY_USERNAME', '')
    HTTP_PROXY_PASSWORD = os.getenv('HTTP_PROXY_PASSWORD', '')
    
    if not HTTP_PROXY_ENABLED:
        print("❌ HTTP Proxy is disabled in .env")
        return False
    
    if not HTTP_PROXY_HOST:
        print("❌ HTTP_PROXY_HOST not configured")
        return False
    
    # Create proxy config
    if HTTP_PROXY_USERNAME and HTTP_PROXY_PASSWORD:
        proxy_url = f"http://{HTTP_PROXY_USERNAME}:{HTTP_PROXY_PASSWORD}@{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}"
    else:
        proxy_url = f"http://{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}"
    
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    print(f"🔗 Testing proxy: {HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}")
    
    try:
        # Test with a simple HTTP request
        response = requests.get(
            'http://httpbin.org/ip', 
            proxies=proxies, 
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Proxy working! IP: {data.get('origin', 'Unknown')}")
            return True
        else:
            print(f"❌ Proxy test failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Proxy connection error: {e}")
        return False

def test_api_with_proxy():
    """Test API endpoint with proxy"""
    print("\n🧪 Testing API with HTTP Proxy...")
    
    url = "http://localhost:8188/forward"
    
    # Test payload with fake data
    payload = {
        "chat_id": "123456789",
        "text_message": "🧪 Test message via HTTP Proxy",
        "bot_token": "123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11",  # Fake token
        "parse_mode": "HTML"
    }
    
    print(f"📤 Sending request to: {url}")
    
    try:
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=15)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f}s")
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code in [200, 400, 401]:  # Expected responses
            print("✅ API is responding correctly")
            return True
        else:
            print("⚠️  Unexpected response")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is server running?")
        return False
    except Exception as e:
        print(f"💥 Error: {e}")
        return False

def test_direct_vs_proxy():
    """Compare direct vs proxy connection"""
    print("\n🔄 Comparing Direct vs Proxy Connection...")
    
    test_url = "https://api.telegram.org/bot123456:test/getMe"
    
    # Test direct connection
    print("📡 Testing direct connection...")
    try:
        start_time = time.time()
        response = requests.get(test_url, timeout=10)
        direct_time = time.time() - start_time
        print(f"⏱️  Direct: {direct_time:.2f}s, Status: {response.status_code}")
    except Exception as e:
        print(f"❌ Direct failed: {e}")
        direct_time = None
    
    # Test proxy connection
    HTTP_PROXY_ENABLED = os.getenv('HTTP_PROXY_ENABLED', 'False').lower() == 'true'
    if HTTP_PROXY_ENABLED:
        print("🔗 Testing proxy connection...")
        
        HTTP_PROXY_HOST = os.getenv('HTTP_PROXY_HOST', '')
        HTTP_PROXY_PORT = int(os.getenv('HTTP_PROXY_PORT', '8080'))
        HTTP_PROXY_USERNAME = os.getenv('HTTP_PROXY_USERNAME', '')
        HTTP_PROXY_PASSWORD = os.getenv('HTTP_PROXY_PASSWORD', '')
        
        if HTTP_PROXY_USERNAME and HTTP_PROXY_PASSWORD:
            proxy_url = f"http://{HTTP_PROXY_USERNAME}:{HTTP_PROXY_PASSWORD}@{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}"
        else:
            proxy_url = f"http://{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}"
        
        proxies = {'http': proxy_url, 'https': proxy_url}
        
        try:
            start_time = time.time()
            response = requests.get(test_url, proxies=proxies, timeout=10)
            proxy_time = time.time() - start_time
            print(f"⏱️  Proxy: {proxy_time:.2f}s, Status: {response.status_code}")
            
            if direct_time and proxy_time:
                if proxy_time < direct_time:
                    print("🚀 Proxy is faster!")
                else:
                    print("🐌 Direct is faster")
                    
        except Exception as e:
            print(f"❌ Proxy failed: {e}")
    else:
        print("⚠️  Proxy disabled, skipping proxy test")

def main():
    """Main test function"""
    print("🚀 HTTP PROXY TEST SUITE")
    print("=" * 50)
    
    # Test 1: Proxy connection
    proxy_ok = test_proxy_connection()
    
    # Test 2: API with proxy
    api_ok = test_api_with_proxy()
    
    # Test 3: Performance comparison
    test_direct_vs_proxy()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY:")
    print(f"   Proxy Connection: {'✅ OK' if proxy_ok else '❌ FAIL'}")
    print(f"   API Response: {'✅ OK' if api_ok else '❌ FAIL'}")
    
    if proxy_ok and api_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ HTTP Proxy is working correctly")
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("💡 Check your proxy configuration in .env")

if __name__ == "__main__":
    main()
