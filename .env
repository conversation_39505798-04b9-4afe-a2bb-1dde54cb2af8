# ============================================================================
# TELEGRAM PROXY CONFIGURATION
# ============================================================================

# Flask Server Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=8188
FLASK_DEBUG=False

# ============================================================================
# HTTP PROXY CONFIGURATION
# ============================================================================
# Sử dụng HTTP/HTTPS proxy để route Bot API requests
HTTP_PROXY_ENABLED=True
HTTP_PROXY_HOST=127.0.0.1
HTTP_PROXY_PORT=8080
HTTP_PROXY_USERNAME=
HTTP_PROXY_PASSWORD=

# ============================================================================
# LOGGING & PERFORMANCE
# ============================================================================
LOG_LEVEL=INFO
REQUEST_TIMEOUT=5

# ============================================================================
# EXAMPLE CONFIGURATIONS
# ============================================================================
#
# 1. No Proxy (Default):
#    HTTP_PROXY_ENABLED=False
#
# 2. HTTP Proxy without authentication:
#    HTTP_PROXY_ENABLED=True
#    HTTP_PROXY_HOST=proxy.example.com
#    HTTP_PROXY_PORT=8080
#
# 3. HTTP Proxy with authentication:
#    HTTP_PROXY_ENABLED=True
#    HTTP_PROXY_HOST=proxy.example.com
#    HTTP_PROXY_PORT=8080
#    HTTP_PROXY_USERNAME=your_username
#    HTTP_PROXY_PASSWORD=your_password
#
# 4. SOCKS5 Proxy (requires PySocks):
#    HTTP_PROXY_ENABLED=True
#    HTTP_PROXY_HOST=socks5://proxy.example.com
#    HTTP_PROXY_PORT=1080
#
# ============================================================================
