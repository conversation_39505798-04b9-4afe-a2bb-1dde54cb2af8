import logging
import os
import asyncio
from flask import Flask, request, jsonify
from pyrogram import Client
from pyrogram.errors import FloodWait, RPCError
from dotenv import load_dotenv
import threading
import time

# Load environment variables
load_dotenv()

# <PERSON><PERSON>u hình logging
log_level = getattr(logging, os.getenv('LOG_LEVEL', 'INFO').upper())
logging.basicConfig(level=log_level)

# MTProto Configuration
MTPROTO_ENABLED = os.getenv('MTPROTO_ENABLED', 'False').lower() == 'true'
TELEGRAM_API_ID = int(os.getenv('TELEGRAM_API_ID', '0'))
TELEGRAM_API_HASH = os.getenv('TELEGRAM_API_HASH', '')
TELEGRAM_SESSION_NAME = os.getenv('TELEGRAM_SESSION_NAME', 'telegram_proxy_session')
MTPROTO_TEST_MODE = os.getenv('MTPROTO_TEST_MODE', 'False').lower() == 'true'

# MTProxy Configuration
MTPROXY_ENABLED = os.getenv('MTPROXY_ENABLED', 'False').lower() == 'true'
MTPROXY_HOST = os.getenv('MTPROXY_HOST', '')
MTPROXY_PORT = int(os.getenv('MTPROXY_PORT', '443'))
MTPROXY_SECRET = os.getenv('MTPROXY_SECRET', '')

# Flask Configuration
FLASK_HOST = os.getenv('FLASK_HOST', '0.0.0.0')
FLASK_PORT = int(os.getenv('FLASK_PORT', '8188'))
FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

# Request Configuration
REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '30'))

# Message Sending Strategy
PRIMARY_METHOD = os.getenv('PRIMARY_METHOD', 'bot_api').lower()

# Global Pyrogram client
telegram_client = None

def create_telegram_client():
    """Tạo Pyrogram client với cấu hình MTProxy"""
    global telegram_client

    if not MTPROTO_ENABLED or not TELEGRAM_API_ID or not TELEGRAM_API_HASH:
        logging.warning("MTProto disabled or missing API credentials")
        return None

    try:
        # Cấu hình proxy nếu được bật
        proxy_config = None
        if MTPROXY_ENABLED and MTPROXY_HOST and MTPROXY_SECRET:
            # Pyrogram MTProxy configuration
            proxy_config = {
                "scheme": "mtproto",
                "hostname": MTPROXY_HOST,
                "port": MTPROXY_PORT,
                "secret": MTPROXY_SECRET
            }
            logging.info(f"Using MTProxy: {MTPROXY_HOST}:{MTPROXY_PORT}")
        else:
            logging.info("MTProxy not configured, using direct connection")

        telegram_client = Client(
            name=TELEGRAM_SESSION_NAME,
            api_id=TELEGRAM_API_ID,
            api_hash=TELEGRAM_API_HASH,
            test_mode=MTPROTO_TEST_MODE,
            proxy=proxy_config,
            no_updates=True  # Không nhận updates để tối ưu hiệu suất
        )

        logging.info(f"Created MTProto client with session: {TELEGRAM_SESSION_NAME}")
        return telegram_client
    except Exception as e:
        logging.error(f"Failed to create MTProto client: {e}")
        return None

async def send_message_via_mtproto(chat_id, text_message, parse_mode='HTML'):
    """Gửi message qua MTProto"""
    try:
        if not telegram_client:
            raise Exception("MTProto client not initialized")

        # Start client nếu chưa start
        if not telegram_client.is_connected:
            await telegram_client.start()

        # Gửi message qua MTProto
        # Note: MTProto sử dụng user account, không cần bot_token
        message = await telegram_client.send_message(
            chat_id=chat_id,
            text=text_message,
            parse_mode=parse_mode.lower() if parse_mode else None
        )

        logging.info(f"Message sent via MTProto to chat_id {chat_id}")
        return True, "Message sent successfully via MTProto"

    except FloodWait as e:
        logging.warning(f"FloodWait: waiting {e.value} seconds")
        await asyncio.sleep(e.value)
        return False, f"Rate limited, wait {e.value} seconds"
    except RPCError as e:
        logging.error(f"MTProto RPC Error: {e}")
        return False, f"MTProto error: {e}"
    except Exception as e:
        logging.error(f"MTProto send error: {e}")
        return False, f"MTProto error: {e}"

def run_async_in_thread(coro):
    """Chạy async function trong thread riêng"""
    def run():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    import concurrent.futures
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(run)
        return future.result(timeout=REQUEST_TIMEOUT)

def send_via_bot_api(bot_token, chat_id, text_message, parse_mode, fallback_to_mtproto=False):
    """Gửi message qua Bot API với fallback option"""
    import requests

    logging.info("Sending message via Bot API")

    telegram_api_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    payload = {
        'chat_id': chat_id,
        'text': text_message,
        'parse_mode': parse_mode
    }

    try:
        response = requests.post(telegram_api_url, json=payload, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            logging.info(f"Message sent to chat_id {chat_id} via Bot API")
            return jsonify({'status': 'Message sent successfully via Bot API'}), 200
        else:
            logging.error(f"Bot API error: {response.text}")

            # Fallback to MTProto if enabled
            if fallback_to_mtproto:
                logging.warning("Bot API failed, trying MTProto+MTProxy fallback...")
                success, message = run_async_in_thread(
                    send_message_via_mtproto(chat_id, text_message, parse_mode)
                )

                if success:
                    return jsonify({'status': f'Message sent via MTProto+MTProxy fallback: {message}'}), 200
                else:
                    logging.error(f"MTProto fallback also failed: {message}")

            return jsonify({'error': response.text}), response.status_code

    except Exception as e:
        logging.error(f"Bot API request error: {e}")

        # Fallback to MTProto if enabled
        if fallback_to_mtproto:
            try:
                logging.warning("Bot API network error, trying MTProto+MTProxy fallback...")
                success, message = run_async_in_thread(
                    send_message_via_mtproto(chat_id, text_message, parse_mode)
                )

                if success:
                    return jsonify({'status': f'Message sent via MTProto+MTProxy fallback: {message}'}), 200
                else:
                    logging.error(f"MTProto fallback failed: {message}")
            except Exception as mtproto_error:
                logging.error(f"MTProto fallback error: {mtproto_error}")

        return jsonify({'error': str(e)}), 500

# Tạo Flask app
app = Flask(__name__)

@app.route('/forward', methods=['POST'])
def forward_message():
    data = request.get_json()

    # Log toàn bộ request body
    logging.info(f"Received request with body: {data}")

    chat_id = data.get('chat_id')
    text_message = data.get('text_message')
    bot_token = data.get('bot_token')
    parse_mode = data.get('parse_mode', 'HTML')

    if not chat_id or not text_message or not bot_token:
        return jsonify({'error': 'Missing required fields'}), 400

    # Đảm bảo chat_id là chuỗi
    chat_id = str(chat_id)

    # Log thông tin chat_id và bot_token để kiểm tra
    logging.info(f"Chat ID: {chat_id}, Bot Token: {bot_token}")

    try:
        # Chọn phương thức chính dựa trên cấu hình
        if PRIMARY_METHOD == 'mtproto' and MTPROTO_ENABLED and telegram_client:
            # Phương thức chính: MTProto qua MTProxy
            logging.info("Sending message via MTProto+MTProxy (primary)")
            success, message = run_async_in_thread(
                send_message_via_mtproto(chat_id, text_message, parse_mode)
            )

            if success:
                return jsonify({'status': f'Message sent via MTProto+MTProxy: {message}'}), 200
            else:
                # Fallback: Bot API
                logging.warning(f"MTProto failed: {message}, falling back to Bot API")
                return send_via_bot_api(bot_token, chat_id, text_message, parse_mode)
        else:
            # Phương thức chính: Bot API
            return send_via_bot_api(bot_token, chat_id, text_message, parse_mode,
                                  fallback_to_mtproto=MTPROTO_ENABLED and telegram_client)

    except Exception as e:
        logging.error(f"Error in forward_message: {e}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Khởi tạo MTProto client
    if MTPROTO_ENABLED:
        logging.info("Initializing MTProto client...")
        create_telegram_client()
        if telegram_client:
            logging.info("MTProto client initialized successfully")
        else:
            logging.warning("Failed to initialize MTProto client, will use Bot API fallback")
    else:
        logging.info("MTProto disabled, using Bot API only")

    # Start Flask app
    logging.info(f"Starting Flask app on {FLASK_HOST}:{FLASK_PORT}")
    app.run(host=FLASK_HOST, port=FLASK_PORT, debug=FLASK_DEBUG)
